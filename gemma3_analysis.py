#!/usr/bin/env python3
"""
1B Model Analysis Script (Gemma3-style analysis)
This script demonstrates analyzing a 1B model architecture and configuration.
Shows the methodology that would apply to Gemma3 1B with int8 quantization.
"""

import torch
import time
import json
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    AutoConfig,
    BitsAndBytesConfig
)
from collections import OrderedDict
import psutil


def get_system_info():
    """Get system information for context."""
    return {
        "cpu_count": psutil.cpu_count(),
        "memory_gb": round(psutil.virtual_memory().total / (1024**3), 2),
        "torch_version": torch.__version__,
        "device": "cpu" if not torch.cuda.is_available() else "cuda"
    }


def analyze_gemma3_config_only():
    """Analyze Gemma3 1B model configuration without loading weights."""
    print("Analyzing Gemma3 1B model configuration...")

    model_name = "google/gemma-3-1b-it"

    try:
        # Load configuration and tokenizer only (no model weights)
        print("Loading configuration...")
        config = AutoConfig.from_pretrained(model_name)

        print("Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_name)

        # Extract all configuration attributes
        config_dict = {}
        for attr in dir(config):
            if not attr.startswith('_') and not callable(getattr(config, attr)):
                try:
                    value = getattr(config, attr)
                    # Convert to serializable format
                    if isinstance(value, (int, float, str, bool, list, dict, type(None))):
                        config_dict[attr] = value
                    else:
                        config_dict[attr] = str(value)
                except:
                    config_dict[attr] = "Unable to serialize"

        # Calculate theoretical parameter counts
        vocab_size = config.vocab_size
        hidden_size = config.hidden_size
        num_layers = config.num_hidden_layers
        num_attention_heads = config.num_attention_heads
        num_key_value_heads = getattr(
            config, 'num_key_value_heads', num_attention_heads)
        intermediate_size = config.intermediate_size
        head_dim = hidden_size // num_attention_heads

        # Detailed parameter breakdown
        embedding_params = vocab_size * hidden_size

        # Per layer calculations
        # Attention: Q, K, V, O projections
        q_proj_params = hidden_size * num_attention_heads * head_dim
        k_proj_params = hidden_size * num_key_value_heads * head_dim
        v_proj_params = hidden_size * num_key_value_heads * head_dim
        o_proj_params = num_attention_heads * head_dim * hidden_size
        attention_params_per_layer = q_proj_params + \
            k_proj_params + v_proj_params + o_proj_params

        # MLP: gate, up, down projections
        gate_proj_params = hidden_size * intermediate_size
        up_proj_params = hidden_size * intermediate_size
        down_proj_params = intermediate_size * hidden_size
        mlp_params_per_layer = gate_proj_params + up_proj_params + down_proj_params

        # Layer norms
        input_layernorm_params = hidden_size
        post_attention_layernorm_params = hidden_size
        norm_params_per_layer = input_layernorm_params + post_attention_layernorm_params

        total_params_per_layer = attention_params_per_layer + \
            mlp_params_per_layer + norm_params_per_layer
        total_layer_params = num_layers * total_params_per_layer

        # Final layer norm and LM head
        final_norm_params = hidden_size
        lm_head_params = vocab_size * hidden_size

        # Total (assuming weight sharing between embedding and lm_head)
        total_params_no_sharing = embedding_params + \
            total_layer_params + final_norm_params + lm_head_params
        total_params_with_sharing = embedding_params + total_layer_params + \
            final_norm_params  # lm_head shares with embedding

        # Create comprehensive analysis
        analysis = {
            "model_name": model_name,
            "full_config": config_dict,
            "architecture_summary": {
                "model_type": config.model_type,
                "vocab_size": vocab_size,
                "hidden_size": hidden_size,
                "num_layers": num_layers,
                "num_attention_heads": num_attention_heads,
                "num_key_value_heads": num_key_value_heads,
                "intermediate_size": intermediate_size,
                "head_dim": head_dim,
                "max_position_embeddings": config.max_position_embeddings,
                "rms_norm_eps": getattr(config, 'rms_norm_eps', 1e-6),
                "rope_theta": getattr(config, 'rope_theta', 10000.0),
                "attention_bias": getattr(config, 'attention_bias', False),
                "mlp_bias": getattr(config, 'mlp_bias', False),
            },
            "parameter_breakdown": {
                "embedding_parameters": embedding_params,
                "per_layer_breakdown": {
                    "attention": {
                        "q_proj": q_proj_params,
                        "k_proj": k_proj_params,
                        "v_proj": v_proj_params,
                        "o_proj": o_proj_params,
                        "total_attention": attention_params_per_layer
                    },
                    "mlp": {
                        "gate_proj": gate_proj_params,
                        "up_proj": up_proj_params,
                        "down_proj": down_proj_params,
                        "total_mlp": mlp_params_per_layer
                    },
                    "layer_norms": {
                        "input_layernorm": input_layernorm_params,
                        "post_attention_layernorm": post_attention_layernorm_params,
                        "total_norms": norm_params_per_layer
                    },
                    "total_per_layer": total_params_per_layer
                },
                "total_layer_params": total_layer_params,
                "final_norm_params": final_norm_params,
                "lm_head_params": lm_head_params,
                "total_without_sharing": total_params_no_sharing,
                "total_with_sharing": total_params_with_sharing,
                "total_millions": round(total_params_with_sharing / 1e6, 2)
            },
            "tokenizer_info": {
                "vocab_size": len(tokenizer),
                "model_max_length": tokenizer.model_max_length,
                "pad_token": str(tokenizer.pad_token),
                "eos_token": str(tokenizer.eos_token),
                "bos_token": str(tokenizer.bos_token),
                "unk_token": str(tokenizer.unk_token),
                "special_tokens": {
                    "pad_token_id": tokenizer.pad_token_id,
                    "eos_token_id": tokenizer.eos_token_id,
                    "bos_token_id": tokenizer.bos_token_id,
                    "unk_token_id": tokenizer.unk_token_id,
                }
            }
        }

        # Print summary
        print(f"\nGEMMA3 1B MODEL CONFIGURATION ANALYSIS:")
        print(f"Model type: {config.model_type}")
        print(
            f"Total parameters (with sharing): {total_params_with_sharing:,} ({round(total_params_with_sharing / 1e6, 2)}M)")
        print(f"Hidden size: {hidden_size}")
        print(f"Number of layers: {num_layers}")
        print(f"Attention heads: {num_attention_heads}")
        print(f"Key-value heads: {num_key_value_heads}")
        print(f"Head dimension: {head_dim}")
        print(f"Intermediate size: {intermediate_size}")
        print(f"Max position embeddings: {config.max_position_embeddings}")
        print(f"Vocabulary size: {vocab_size}")

        return analysis

    except Exception as e:
        print(f"Error analyzing Gemma3 model: {e}")
        print("This might be due to:")
        print("1. Model access restrictions (need to accept license)")
        print("2. Authentication required (huggingface-cli login)")
        print("3. Network connectivity issues")
        return None


def create_detailed_layer_analysis(analysis):
    """Create detailed analysis of each layer with shapes and parameter counts."""
    if not analysis:
        return None

    print("\nCreating detailed layer analysis...")

    config = analysis["architecture_summary"]
    param_breakdown = analysis["parameter_breakdown"]

    layers_analysis = {
        "model_overview": {
            "total_layers": config["num_layers"],
            "total_parameters": param_breakdown["total_with_sharing"],
            "total_parameters_millions": param_breakdown["total_millions"]
        },
        "embedding_layer": {
            "name": "embed_tokens",
            "type": "Embedding",
            "shape": [config["vocab_size"], config["hidden_size"]],
            "parameters": param_breakdown["embedding_parameters"],
            "parameters_millions": round(param_breakdown["embedding_parameters"] / 1e6, 2),
            "description": "Token embeddings"
        },
        "transformer_layers": []
    }

    # Create detailed analysis for each transformer layer
    for layer_idx in range(config["num_layers"]):
        layer_analysis = {
            "layer_index": layer_idx,
            "layer_name": f"model.layers.{layer_idx}",
            "total_parameters": param_breakdown["per_layer_breakdown"]["total_per_layer"],
            "total_parameters_millions": round(param_breakdown["per_layer_breakdown"]["total_per_layer"] / 1e6, 2),
            "components": {
                "input_layernorm": {
                    "name": f"model.layers.{layer_idx}.input_layernorm",
                    "type": "RMSNorm",
                    "shape": [config["hidden_size"]],
                    "parameters": param_breakdown["per_layer_breakdown"]["layer_norms"]["input_layernorm"],
                    "description": "Pre-attention layer normalization"
                },
                "self_attention": {
                    "name": f"model.layers.{layer_idx}.self_attn",
                    "type": "GemmaAttention",
                    "total_parameters": param_breakdown["per_layer_breakdown"]["attention"]["total_attention"],
                    "total_parameters_millions": round(param_breakdown["per_layer_breakdown"]["attention"]["total_attention"] / 1e6, 2),
                    "projections": {
                        "q_proj": {
                            "name": f"model.layers.{layer_idx}.self_attn.q_proj",
                            "shape": [config["hidden_size"], config["num_attention_heads"] * config["head_dim"]],
                            "parameters": param_breakdown["per_layer_breakdown"]["attention"]["q_proj"],
                            "description": "Query projection"
                        },
                        "k_proj": {
                            "name": f"model.layers.{layer_idx}.self_attn.k_proj",
                            "shape": [config["hidden_size"], config["num_key_value_heads"] * config["head_dim"]],
                            "parameters": param_breakdown["per_layer_breakdown"]["attention"]["k_proj"],
                            "description": "Key projection"
                        },
                        "v_proj": {
                            "name": f"model.layers.{layer_idx}.self_attn.v_proj",
                            "shape": [config["hidden_size"], config["num_key_value_heads"] * config["head_dim"]],
                            "parameters": param_breakdown["per_layer_breakdown"]["attention"]["v_proj"],
                            "description": "Value projection"
                        },
                        "o_proj": {
                            "name": f"model.layers.{layer_idx}.self_attn.o_proj",
                            "shape": [config["num_attention_heads"] * config["head_dim"], config["hidden_size"]],
                            "parameters": param_breakdown["per_layer_breakdown"]["attention"]["o_proj"],
                            "description": "Output projection"
                        }
                    },
                    "attention_config": {
                        "num_heads": config["num_attention_heads"],
                        "num_key_value_heads": config["num_key_value_heads"],
                        "head_dim": config["head_dim"],
                        "is_grouped_query_attention": config["num_key_value_heads"] != config["num_attention_heads"]
                    }
                },
                "post_attention_layernorm": {
                    "name": f"model.layers.{layer_idx}.post_attention_layernorm",
                    "type": "RMSNorm",
                    "shape": [config["hidden_size"]],
                    "parameters": param_breakdown["per_layer_breakdown"]["layer_norms"]["post_attention_layernorm"],
                    "description": "Pre-MLP layer normalization"
                },
                "mlp": {
                    "name": f"model.layers.{layer_idx}.mlp",
                    "type": "GemmaMLP",
                    "total_parameters": param_breakdown["per_layer_breakdown"]["mlp"]["total_mlp"],
                    "total_parameters_millions": round(param_breakdown["per_layer_breakdown"]["mlp"]["total_mlp"] / 1e6, 2),
                    "projections": {
                        "gate_proj": {
                            "name": f"model.layers.{layer_idx}.mlp.gate_proj",
                            "shape": [config["hidden_size"], config["intermediate_size"]],
                            "parameters": param_breakdown["per_layer_breakdown"]["mlp"]["gate_proj"],
                            "description": "Gate projection (for SwiGLU activation)"
                        },
                        "up_proj": {
                            "name": f"model.layers.{layer_idx}.mlp.up_proj",
                            "shape": [config["hidden_size"], config["intermediate_size"]],
                            "parameters": param_breakdown["per_layer_breakdown"]["mlp"]["up_proj"],
                            "description": "Up projection (for SwiGLU activation)"
                        },
                        "down_proj": {
                            "name": f"model.layers.{layer_idx}.mlp.down_proj",
                            "shape": [config["intermediate_size"], config["hidden_size"]],
                            "parameters": param_breakdown["per_layer_breakdown"]["mlp"]["down_proj"],
                            "description": "Down projection"
                        }
                    },
                    "activation": "SwiGLU"
                }
            }
        }
        layers_analysis["transformer_layers"].append(layer_analysis)

    # Final components
    layers_analysis["final_components"] = {
        "norm": {
            "name": "model.norm",
            "type": "RMSNorm",
            "shape": [config["hidden_size"]],
            "parameters": param_breakdown["final_norm_params"],
            "description": "Final layer normalization"
        },
        "lm_head": {
            "name": "lm_head",
            "type": "Linear",
            "shape": [config["hidden_size"], config["vocab_size"]],
            "parameters": param_breakdown["lm_head_params"],
            "parameters_millions": round(param_breakdown["lm_head_params"] / 1e6, 2),
            "description": "Language modeling head (often shares weights with embedding)",
            "weight_sharing": "Shares weights with embed_tokens"
        }
    }

    return layers_analysis


def inspect_model_architecture(model):
    """Inspect and analyze the model architecture."""
    print("\n" + "="*60)
    print("MODEL ARCHITECTURE ANALYSIS")
    print("="*60)

    architecture_info = OrderedDict()

    # Basic model info
    architecture_info["model_type"] = type(model).__name__
    architecture_info["config"] = {
        "vocab_size": model.config.vocab_size,
        "hidden_size": model.config.hidden_size,
        "num_hidden_layers": model.config.num_hidden_layers,
        "num_attention_heads": model.config.num_attention_heads,
        "intermediate_size": model.config.intermediate_size,
        "max_position_embeddings": getattr(model.config, 'max_position_embeddings', 'N/A'),
        "rms_norm_eps": getattr(model.config, 'rms_norm_eps', 'N/A'),
        "rope_theta": getattr(model.config, 'rope_theta', 'N/A'),
    }

    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel()
                           for p in model.parameters() if p.requires_grad)

    architecture_info["parameters"] = {
        "total": total_params,
        "trainable": trainable_params,
        "total_millions": round(total_params / 1e6, 2),
        "trainable_millions": round(trainable_params / 1e6, 2)
    }

    # Analyze layers
    print("\nLAYER-BY-LAYER ANALYSIS:")
    print("-" * 40)

    layer_info = []

    for name, module in model.named_modules():
        if len(list(module.children())) == 0:  # Leaf modules only
            param_count = sum(p.numel() for p in module.parameters())
            if param_count > 0:
                layer_data = {
                    "name": name,
                    "type": type(module).__name__,
                    "parameters": param_count,
                    "parameters_millions": round(param_count / 1e6, 4),
                    "shape": str(list(module.parameters())[0].shape) if list(module.parameters()) else "N/A",
                    "dtype": str(list(module.parameters())[0].dtype) if list(module.parameters()) else "N/A",
                    "device": str(list(module.parameters())[0].device) if list(module.parameters()) else "N/A"
                }
                layer_info.append(layer_data)

                # Print layer info
                print(f"Layer: {name}")
                print(f"  Type: {layer_data['type']}")
                print(
                    f"  Parameters: {layer_data['parameters']:,} ({layer_data['parameters_millions']}M)")
                print(f"  Shape: {layer_data['shape']}")
                print(f"  Dtype: {layer_data['dtype']}")
                print(f"  Device: {layer_data['device']}")
                print()

    architecture_info["layers"] = layer_info

    # Group layers by type
    layer_types = {}
    for layer in layer_info:
        layer_type = layer["type"]
        if layer_type not in layer_types:
            layer_types[layer_type] = {"count": 0, "total_params": 0}
        layer_types[layer_type]["count"] += 1
        layer_types[layer_type]["total_params"] += layer["parameters"]

    print("LAYER TYPE SUMMARY:")
    print("-" * 40)
    for layer_type, info in layer_types.items():
        print(
            f"{layer_type}: {info['count']} layers, {info['total_params']:,} parameters ({round(info['total_params']/1e6, 2)}M)")

    architecture_info["layer_type_summary"] = layer_types

    return architecture_info


def test_gemma3_chat(model, tokenizer):
    """Test Gemma3 model with chat template."""
    print("\n" + "="*60)
    print("GEMMA3 CHAT FUNCTIONALITY TEST")
    print("="*60)

    # Test messages
    messages = [
        {"role": "user", "content": "Who are you?"},
    ]

    try:
        # Apply chat template
        inputs = tokenizer.apply_chat_template(
            messages,
            add_generation_prompt=True,
            tokenize=True,
            return_dict=True,
            return_tensors="pt",
        ).to(model.device)

        print(f"Input shape: {inputs['input_ids'].shape}")
        print(f"Input text: {tokenizer.decode(inputs['input_ids'][0])}")

        # Generate response
        print("Generating response...")
        start_time = time.time()

        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=40,
                do_sample=True,
                temperature=0.7,
                pad_token_id=tokenizer.eos_token_id
            )

        generation_time = time.time() - start_time

        # Decode response
        response = tokenizer.decode(
            outputs[0][inputs["input_ids"].shape[-1]:], skip_special_tokens=True)

        print(f"Response: {response}")
        print(f"Generation time: {generation_time:.3f}s")
        print(
            f"Tokens generated: {outputs.shape[1] - inputs['input_ids'].shape[1]}")
        print(
            f"Generation speed: {(outputs.shape[1] - inputs['input_ids'].shape[1]) / generation_time:.2f} tok/sec")

        return {
            "input_text": tokenizer.decode(inputs['input_ids'][0]),
            "response": response,
            "generation_time": generation_time,
            "tokens_generated": outputs.shape[1] - inputs['input_ids'].shape[1],
            "generation_speed": (outputs.shape[1] - inputs['input_ids'].shape[1]) / generation_time
        }

    except Exception as e:
        print(f"Error in chat test: {e}")
        return None


def measure_inference_speed(model, tokenizer, sequence_length=2048):
    """Measure inference speed with dummy input."""
    print("\n" + "="*60)
    print("INFERENCE SPEED MEASUREMENT")
    print("="*60)

    # Create dummy input
    dummy_text = "The future of artificial intelligence is " * \
        50  # Repeat to get longer text

    # Tokenize input
    inputs = tokenizer(
        dummy_text,
        return_tensors="pt",
        max_length=sequence_length,
        truncation=True,
        padding=True
    )

    input_ids = inputs["input_ids"]
    attention_mask = inputs["attention_mask"]

    print(f"Input sequence length: {input_ids.shape[1]}")
    print(f"Target sequence length: {sequence_length}")

    # Move inputs to same device as model
    device = next(model.parameters()).device
    input_ids = input_ids.to(device)
    attention_mask = attention_mask.to(device)

    # Warm-up run
    print("Performing warm-up run...")
    with torch.no_grad():
        _ = model(input_ids, attention_mask=attention_mask)

    # Measure inference speed
    num_runs = 5
    total_time = 0

    print(f"Measuring inference speed over {num_runs} runs...")

    for i in range(num_runs):
        start_time = time.time()

        with torch.no_grad():
            outputs = model(input_ids, attention_mask=attention_mask)

        end_time = time.time()
        run_time = end_time - start_time
        total_time += run_time

        print(f"Run {i+1}: {run_time:.4f} seconds")

    avg_time = total_time / num_runs
    tokens_per_second = input_ids.shape[1] / avg_time

    speed_results = {
        "sequence_length": input_ids.shape[1],
        "num_runs": num_runs,
        "average_time_seconds": round(avg_time, 4),
        "tokens_per_second": round(tokens_per_second, 2),
        "total_time_seconds": round(total_time, 4)
    }

    print(f"\nINFERENCE SPEED RESULTS:")
    print(f"Average time per run: {avg_time:.4f} seconds")
    print(f"Tokens per second: {tokens_per_second:.2f} tok/sec")

    return speed_results


def save_results(architecture_info, speed_results, system_info):
    """Save all results to a JSON file."""
    results = {
        "system_info": system_info,
        "architecture_analysis": architecture_info,
        "inference_speed": speed_results,
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
    }

    with open("gemma3_analysis_results.json", "w") as f:
        json.dump(results, f, indent=2, default=str)

    print(f"\nResults saved to gemma3_analysis_results.json")


def create_gemma3_theoretical_analysis():
    """Create theoretical analysis of Gemma3 1B architecture."""
    print("\n" + "="*60)
    print("GEMMA3 1B THEORETICAL ARCHITECTURE ANALYSIS")
    print("="*60)

    # Gemma3 1B theoretical specifications (based on typical 1B model architecture)
    gemma3_specs = {
        "model_name": "Gemma3 1B (Theoretical)",
        "config": {
            "model_type": "gemma3",
            "vocab_size": 256000,  # Typical for Gemma models
            "hidden_size": 2048,   # Common for 1B models
            "num_layers": 18,      # Typical for 1B parameter count
            "num_attention_heads": 16,
            "intermediate_size": 8192,  # Usually 4x hidden_size
            "max_position_embeddings": 2048,
            "rms_norm_eps": 1e-6,
            "rope_theta": 10000.0,
        }
    }

    config = gemma3_specs["config"]

    # Calculate parameter breakdown
    vocab_size = config["vocab_size"]
    hidden_size = config["hidden_size"]
    num_layers = config["num_layers"]
    num_heads = config["num_attention_heads"]
    intermediate_size = config["intermediate_size"]

    # Parameter calculations
    embedding_params = vocab_size * hidden_size

    # Per layer calculations
    attention_params_per_layer = 4 * hidden_size * hidden_size  # Q, K, V, O
    ffn_params_per_layer = 2 * hidden_size * intermediate_size  # gate, up, down
    rms_norm_params_per_layer = 2 * hidden_size  # attention and ffn norms

    total_layer_params = num_layers * \
        (attention_params_per_layer + ffn_params_per_layer + rms_norm_params_per_layer)
    final_norm_params = hidden_size
    lm_head_params = vocab_size * hidden_size  # Often shared with embeddings

    total_params = embedding_params + total_layer_params + \
        final_norm_params + lm_head_params

    # With weight sharing (embedding and lm_head), subtract one copy
    total_params_shared = total_params - lm_head_params

    gemma3_specs["parameter_breakdown"] = {
        "embedding_parameters": embedding_params,
        "attention_params_per_layer": attention_params_per_layer,
        "ffn_params_per_layer": ffn_params_per_layer,
        "rms_norm_params_per_layer": rms_norm_params_per_layer,
        "total_layer_params": total_layer_params,
        "final_norm_params": final_norm_params,
        "lm_head_params": lm_head_params,
        "total_without_sharing": total_params,
        "total_with_sharing": total_params_shared,
        "total_millions": round(total_params_shared / 1e6, 2)
    }

    # Print detailed breakdown
    print(f"Model: {gemma3_specs['model_name']}")
    print(f"Architecture: Transformer decoder with RMSNorm and RoPE")
    print(f"Vocabulary size: {vocab_size:,}")
    print(f"Hidden size: {hidden_size}")
    print(f"Number of layers: {num_layers}")
    print(f"Attention heads: {num_heads}")
    print(f"Intermediate size: {intermediate_size}")
    print(f"Max sequence length: {config['max_position_embeddings']}")

    print(f"\nPARAMETER BREAKDOWN:")
    print(
        f"  Embedding parameters: {embedding_params:,} ({round(embedding_params/1e6, 2)}M)")
    print(
        f"  Per-layer attention: {attention_params_per_layer:,} ({round(attention_params_per_layer/1e6, 2)}M)")
    print(
        f"  Per-layer FFN: {ffn_params_per_layer:,} ({round(ffn_params_per_layer/1e6, 2)}M)")
    print(f"  Per-layer norms: {rms_norm_params_per_layer:,}")
    print(
        f"  Total layer params: {total_layer_params:,} ({round(total_layer_params/1e6, 2)}M)")
    print(f"  Final norm: {final_norm_params:,}")
    print(f"  LM head: {lm_head_params:,} ({round(lm_head_params/1e6, 2)}M)")
    print(
        f"  TOTAL (with weight sharing): {total_params_shared:,} ({round(total_params_shared/1e6, 2)}M)")

    return gemma3_specs


def main():
    """Main function to run the complete analysis."""
    print("GEMMA3 1B MODEL CONFIGURATION ANALYSIS")
    print("=" * 60)

    # Get system info
    system_info = get_system_info()
    print("System Information:")
    for key, value in system_info.items():
        print(f"  {key}: {value}")

    # Analyze Gemma3 configuration without loading full model
    analysis = analyze_gemma3_config_only()

    if analysis is None:
        print("Failed to analyze Gemma3 model configuration. Exiting.")
        return

    # Create detailed layer analysis
    layers_analysis = create_detailed_layer_analysis(analysis)

    # Combine all results
    complete_results = {
        "system_info": system_info,
        "model_analysis": analysis,
        "detailed_layers": layers_analysis,
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "analysis_type": "Configuration-only analysis (no model weights loaded)"
    }

    # Save complete results to JSON
    print("\nSaving results to files...")

    # 1. Complete analysis results
    with open("gemma3_complete_analysis.json", "w") as f:
        json.dump(complete_results, f, indent=2, default=str)
    print("✓ Complete analysis saved to: gemma3_complete_analysis.json")

    # 2. Model configuration only
    with open("gemma3_model_config.json", "w") as f:
        json.dump(analysis["full_config"], f, indent=2, default=str)
    print("✓ Full model config saved to: gemma3_model_config.json")

    # 3. Layer details only
    with open("gemma3_layer_details.json", "w") as f:
        json.dump(layers_analysis, f, indent=2, default=str)
    print("✓ Detailed layer analysis saved to: gemma3_layer_details.json")

    # 4. Parameter breakdown summary
    param_summary = {
        "model_name": analysis["model_name"],
        "total_parameters": analysis["parameter_breakdown"]["total_with_sharing"],
        "total_parameters_millions": analysis["parameter_breakdown"]["total_millions"],
        "parameter_breakdown": analysis["parameter_breakdown"],
        "architecture_summary": analysis["architecture_summary"]
    }
    with open("gemma3_parameter_breakdown.json", "w") as f:
        json.dump(param_summary, f, indent=2, default=str)
    print("✓ Parameter breakdown saved to: gemma3_parameter_breakdown.json")

    # 5. Create human-readable summary
    summary_text = f"""GEMMA3 1B MODEL ANALYSIS SUMMARY
{'='*50}

Model: {analysis['model_name']}
Analysis Date: {time.strftime('%Y-%m-%d %H:%M:%S')}

ARCHITECTURE OVERVIEW:
- Model Type: {analysis['architecture_summary']['model_type']}
- Total Parameters: {analysis['parameter_breakdown']['total_with_sharing']:,} ({analysis['parameter_breakdown']['total_millions']}M)
- Hidden Size: {analysis['architecture_summary']['hidden_size']}
- Number of Layers: {analysis['architecture_summary']['num_layers']}
- Attention Heads: {analysis['architecture_summary']['num_attention_heads']}
- Key-Value Heads: {analysis['architecture_summary']['num_key_value_heads']}
- Head Dimension: {analysis['architecture_summary']['head_dim']}
- Intermediate Size: {analysis['architecture_summary']['intermediate_size']}
- Vocabulary Size: {analysis['architecture_summary']['vocab_size']}
- Max Position Embeddings: {analysis['architecture_summary']['max_position_embeddings']}

PARAMETER BREAKDOWN:
- Embedding Parameters: {analysis['parameter_breakdown']['embedding_parameters']:,} ({round(analysis['parameter_breakdown']['embedding_parameters']/1e6, 2)}M)
- Total Layer Parameters: {analysis['parameter_breakdown']['total_layer_params']:,} ({round(analysis['parameter_breakdown']['total_layer_params']/1e6, 2)}M)
- Final Norm Parameters: {analysis['parameter_breakdown']['final_norm_params']:,}
- LM Head Parameters: {analysis['parameter_breakdown']['lm_head_params']:,} ({round(analysis['parameter_breakdown']['lm_head_params']/1e6, 2)}M)

PER-LAYER BREAKDOWN:
- Attention Parameters per Layer: {analysis['parameter_breakdown']['per_layer_breakdown']['attention']['total_attention']:,} ({round(analysis['parameter_breakdown']['per_layer_breakdown']['attention']['total_attention']/1e6, 2)}M)
  - Q Projection: {analysis['parameter_breakdown']['per_layer_breakdown']['attention']['q_proj']:,}
  - K Projection: {analysis['parameter_breakdown']['per_layer_breakdown']['attention']['k_proj']:,}
  - V Projection: {analysis['parameter_breakdown']['per_layer_breakdown']['attention']['v_proj']:,}
  - O Projection: {analysis['parameter_breakdown']['per_layer_breakdown']['attention']['o_proj']:,}
- MLP Parameters per Layer: {analysis['parameter_breakdown']['per_layer_breakdown']['mlp']['total_mlp']:,} ({round(analysis['parameter_breakdown']['per_layer_breakdown']['mlp']['total_mlp']/1e6, 2)}M)
  - Gate Projection: {analysis['parameter_breakdown']['per_layer_breakdown']['mlp']['gate_proj']:,}
  - Up Projection: {analysis['parameter_breakdown']['per_layer_breakdown']['mlp']['up_proj']:,}
  - Down Projection: {analysis['parameter_breakdown']['per_layer_breakdown']['mlp']['down_proj']:,}
- Layer Norm Parameters per Layer: {analysis['parameter_breakdown']['per_layer_breakdown']['layer_norms']['total_norms']:,}

TOKENIZER INFO:
- Vocabulary Size: {analysis['tokenizer_info']['vocab_size']:,}
- Model Max Length: {analysis['tokenizer_info']['model_max_length']}
- Special Tokens:
  - PAD: {analysis['tokenizer_info']['pad_token']} (ID: {analysis['tokenizer_info']['special_tokens']['pad_token_id']})
  - EOS: {analysis['tokenizer_info']['eos_token']} (ID: {analysis['tokenizer_info']['special_tokens']['eos_token_id']})
  - BOS: {analysis['tokenizer_info']['bos_token']} (ID: {analysis['tokenizer_info']['special_tokens']['bos_token_id']})
  - UNK: {analysis['tokenizer_info']['unk_token']} (ID: {analysis['tokenizer_info']['special_tokens']['unk_token_id']})

LAYER STRUCTURE:
Each of the {analysis['architecture_summary']['num_layers']} transformer layers contains:
1. Input Layer Norm (RMSNorm): {analysis['parameter_breakdown']['per_layer_breakdown']['layer_norms']['input_layernorm']:,} parameters
2. Multi-Head Attention: {analysis['parameter_breakdown']['per_layer_breakdown']['attention']['total_attention']:,} parameters
   - Uses Grouped Query Attention (GQA) with {analysis['architecture_summary']['num_key_value_heads']} KV heads
   - Head dimension: {analysis['architecture_summary']['head_dim']}
3. Post-Attention Layer Norm (RMSNorm): {analysis['parameter_breakdown']['per_layer_breakdown']['layer_norms']['post_attention_layernorm']:,} parameters
4. MLP with SwiGLU activation: {analysis['parameter_breakdown']['per_layer_breakdown']['mlp']['total_mlp']:,} parameters

MEMORY REQUIREMENTS (Estimated):
- Model size (fp16): ~{round(analysis['parameter_breakdown']['total_with_sharing'] * 2 / 1e9, 2)} GB
- Model size (int8): ~{round(analysis['parameter_breakdown']['total_with_sharing'] / 1e9, 2)} GB
- Runtime memory (additional): ~1-2 GB for activations
- Total memory needed (int8): ~{round(analysis['parameter_breakdown']['total_with_sharing'] / 1e9 + 1.5, 1)} GB

FILES GENERATED:
- gemma3_complete_analysis.json: Complete analysis with all details
- gemma3_model_config.json: Full model configuration
- gemma3_layer_details.json: Detailed layer-by-layer analysis
- gemma3_parameter_breakdown.json: Parameter count breakdown
- gemma3_analysis_summary.txt: This human-readable summary

Note: This analysis was performed using configuration files only, without loading model weights.
For actual inference testing, the full model would need to be loaded with appropriate hardware resources.
"""

    with open("gemma3_analysis_summary.txt", "w") as f:
        f.write(summary_text)
    print("✓ Human-readable summary saved to: gemma3_analysis_summary.txt")

    print(f"\n{'='*60}")
    print("ANALYSIS COMPLETE!")
    print(f"{'='*60}")
    print(
        f"Total parameters: {analysis['parameter_breakdown']['total_with_sharing']:,} ({analysis['parameter_breakdown']['total_millions']}M)")
    print(
        f"Model architecture: {analysis['architecture_summary']['num_layers']} layers, {analysis['architecture_summary']['num_attention_heads']} attention heads")
    print(
        f"Memory requirement (int8): ~{round(analysis['parameter_breakdown']['total_with_sharing'] / 1e9 + 1.5, 1)} GB")
    print("\nAll analysis files have been saved successfully!")


if __name__ == "__main__":
    main()
