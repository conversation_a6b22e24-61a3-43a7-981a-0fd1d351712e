#!/usr/bin/env python3
"""
1B Model Analysis Script (Gemma3-style analysis)
This script demonstrates analyzing a 1B model architecture and configuration.
Shows the methodology that would apply to Gemma3 1B with int8 quantization.
"""

import torch
import time
import json
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    AutoConfig
)
from collections import OrderedDict
import psutil


def get_system_info():
    """Get system information for context."""
    return {
        "cpu_count": psutil.cpu_count(),
        "memory_gb": round(psutil.virtual_memory().total / (1024**3), 2),
        "torch_version": torch.__version__,
        "device": "cpu" if not torch.cuda.is_available() else "cuda"
    }


def analyze_model_config():
    """Analyze model configuration without loading full weights."""
    print("Analyzing model configuration...")

    # Analyze multiple 1B models for comparison
    models_to_analyze = [
        "TinyLlama/TinyLlama-1.1B-Chat-v1.0",
        "microsoft/DialoGPT-medium",  # ~345M params for comparison
    ]

    results = {}

    for model_name in models_to_analyze:
        try:
            print(f"\nAnalyzing {model_name}...")

            # Load configuration only (no weights)
            config = AutoConfig.from_pretrained(model_name)
            tokenizer = AutoTokenizer.from_pretrained(model_name)

            # Set pad token if not present
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token

            # Calculate theoretical parameter count
            vocab_size = config.vocab_size
            hidden_size = config.hidden_size
            num_layers = config.num_hidden_layers if hasattr(
                config, 'num_hidden_layers') else config.n_layer
            num_heads = config.num_attention_heads if hasattr(
                config, 'num_attention_heads') else config.n_head
            intermediate_size = getattr(
                config, 'intermediate_size', hidden_size * 4)

            # Estimate parameters
            embedding_params = vocab_size * hidden_size
            attention_params_per_layer = 4 * hidden_size * \
                hidden_size  # Q, K, V, O projections
            ffn_params_per_layer = 2 * hidden_size * \
                intermediate_size  # up and down projections
            layer_norm_params_per_layer = 2 * hidden_size  # attention and ffn layer norms

            total_layer_params = num_layers * \
                (attention_params_per_layer +
                 ffn_params_per_layer + layer_norm_params_per_layer)
            final_layer_norm = hidden_size
            lm_head_params = vocab_size * hidden_size

            estimated_total_params = embedding_params + \
                total_layer_params + final_layer_norm + lm_head_params

            results[model_name] = {
                "config": {
                    "model_type": config.model_type,
                    "vocab_size": vocab_size,
                    "hidden_size": hidden_size,
                    "num_layers": num_layers,
                    "num_attention_heads": num_heads,
                    "intermediate_size": intermediate_size,
                    "max_position_embeddings": getattr(config, 'max_position_embeddings', 'N/A'),
                },
                "estimated_parameters": {
                    "embedding_params": embedding_params,
                    "attention_params_per_layer": attention_params_per_layer,
                    "ffn_params_per_layer": ffn_params_per_layer,
                    "layer_norm_params_per_layer": layer_norm_params_per_layer,
                    "total_layer_params": total_layer_params,
                    "lm_head_params": lm_head_params,
                    "estimated_total": estimated_total_params,
                    "estimated_total_millions": round(estimated_total_params / 1e6, 2)
                },
                "tokenizer_info": {
                    "vocab_size": len(tokenizer),
                    "model_max_length": tokenizer.model_max_length,
                    "pad_token": str(tokenizer.pad_token),
                    "eos_token": str(tokenizer.eos_token),
                }
            }

            print(f"  Model type: {config.model_type}")
            print(
                f"  Estimated parameters: {estimated_total_params:,} ({round(estimated_total_params / 1e6, 2)}M)")
            print(f"  Hidden size: {hidden_size}")
            print(f"  Number of layers: {num_layers}")
            print(f"  Attention heads: {num_heads}")

        except Exception as e:
            print(f"Error analyzing {model_name}: {e}")
            results[model_name] = {"error": str(e)}

    return results


def inspect_model_architecture(model):
    """Inspect and analyze the model architecture."""
    print("\n" + "="*60)
    print("MODEL ARCHITECTURE ANALYSIS")
    print("="*60)

    architecture_info = OrderedDict()

    # Basic model info
    architecture_info["model_type"] = type(model).__name__
    architecture_info["config"] = {
        "vocab_size": model.config.vocab_size,
        "hidden_size": model.config.hidden_size,
        "num_hidden_layers": model.config.num_hidden_layers,
        "num_attention_heads": model.config.num_attention_heads,
        "intermediate_size": model.config.intermediate_size,
        "max_position_embeddings": getattr(model.config, 'max_position_embeddings', 'N/A'),
        "rms_norm_eps": getattr(model.config, 'rms_norm_eps', 'N/A'),
        "rope_theta": getattr(model.config, 'rope_theta', 'N/A'),
    }

    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel()
                           for p in model.parameters() if p.requires_grad)

    architecture_info["parameters"] = {
        "total": total_params,
        "trainable": trainable_params,
        "total_millions": round(total_params / 1e6, 2),
        "trainable_millions": round(trainable_params / 1e6, 2)
    }

    # Analyze layers
    print("\nLAYER-BY-LAYER ANALYSIS:")
    print("-" * 40)

    layer_info = []

    for name, module in model.named_modules():
        if len(list(module.children())) == 0:  # Leaf modules only
            param_count = sum(p.numel() for p in module.parameters())
            if param_count > 0:
                layer_data = {
                    "name": name,
                    "type": type(module).__name__,
                    "parameters": param_count,
                    "parameters_millions": round(param_count / 1e6, 4),
                    "shape": str(list(module.parameters())[0].shape) if list(module.parameters()) else "N/A",
                    "dtype": str(list(module.parameters())[0].dtype) if list(module.parameters()) else "N/A",
                    "device": str(list(module.parameters())[0].device) if list(module.parameters()) else "N/A"
                }
                layer_info.append(layer_data)

                # Print layer info
                print(f"Layer: {name}")
                print(f"  Type: {layer_data['type']}")
                print(
                    f"  Parameters: {layer_data['parameters']:,} ({layer_data['parameters_millions']}M)")
                print(f"  Shape: {layer_data['shape']}")
                print(f"  Dtype: {layer_data['dtype']}")
                print(f"  Device: {layer_data['device']}")
                print()

    architecture_info["layers"] = layer_info

    # Group layers by type
    layer_types = {}
    for layer in layer_info:
        layer_type = layer["type"]
        if layer_type not in layer_types:
            layer_types[layer_type] = {"count": 0, "total_params": 0}
        layer_types[layer_type]["count"] += 1
        layer_types[layer_type]["total_params"] += layer["parameters"]

    print("LAYER TYPE SUMMARY:")
    print("-" * 40)
    for layer_type, info in layer_types.items():
        print(
            f"{layer_type}: {info['count']} layers, {info['total_params']:,} parameters ({round(info['total_params']/1e6, 2)}M)")

    architecture_info["layer_type_summary"] = layer_types

    return architecture_info


def measure_inference_speed(model, tokenizer, sequence_length=2048):
    """Measure inference speed with dummy input."""
    print("\n" + "="*60)
    print("INFERENCE SPEED MEASUREMENT")
    print("="*60)

    # Create dummy input
    dummy_text = "The future of artificial intelligence is " * \
        50  # Repeat to get longer text

    # Tokenize input
    inputs = tokenizer(
        dummy_text,
        return_tensors="pt",
        max_length=sequence_length,
        truncation=True,
        padding=True
    )

    input_ids = inputs["input_ids"]
    attention_mask = inputs["attention_mask"]

    print(f"Input sequence length: {input_ids.shape[1]}")
    print(f"Target sequence length: {sequence_length}")

    # Move inputs to same device as model
    device = next(model.parameters()).device
    input_ids = input_ids.to(device)
    attention_mask = attention_mask.to(device)

    # Warm-up run
    print("Performing warm-up run...")
    with torch.no_grad():
        _ = model(input_ids, attention_mask=attention_mask)

    # Measure inference speed
    num_runs = 5
    total_time = 0

    print(f"Measuring inference speed over {num_runs} runs...")

    for i in range(num_runs):
        start_time = time.time()

        with torch.no_grad():
            outputs = model(input_ids, attention_mask=attention_mask)

        end_time = time.time()
        run_time = end_time - start_time
        total_time += run_time

        print(f"Run {i+1}: {run_time:.4f} seconds")

    avg_time = total_time / num_runs
    tokens_per_second = input_ids.shape[1] / avg_time

    speed_results = {
        "sequence_length": input_ids.shape[1],
        "num_runs": num_runs,
        "average_time_seconds": round(avg_time, 4),
        "tokens_per_second": round(tokens_per_second, 2),
        "total_time_seconds": round(total_time, 4)
    }

    print(f"\nINFERENCE SPEED RESULTS:")
    print(f"Average time per run: {avg_time:.4f} seconds")
    print(f"Tokens per second: {tokens_per_second:.2f} tok/sec")

    return speed_results


def save_results(architecture_info, speed_results, system_info):
    """Save all results to a JSON file."""
    results = {
        "system_info": system_info,
        "architecture_analysis": architecture_info,
        "inference_speed": speed_results,
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
    }

    with open("gemma3_analysis_results.json", "w") as f:
        json.dump(results, f, indent=2, default=str)

    print(f"\nResults saved to gemma3_analysis_results.json")


def create_gemma3_theoretical_analysis():
    """Create theoretical analysis of Gemma3 1B architecture."""
    print("\n" + "="*60)
    print("GEMMA3 1B THEORETICAL ARCHITECTURE ANALYSIS")
    print("="*60)

    # Gemma3 1B theoretical specifications (based on typical 1B model architecture)
    gemma3_specs = {
        "model_name": "Gemma3 1B (Theoretical)",
        "config": {
            "model_type": "gemma3",
            "vocab_size": 256000,  # Typical for Gemma models
            "hidden_size": 2048,   # Common for 1B models
            "num_layers": 18,      # Typical for 1B parameter count
            "num_attention_heads": 16,
            "intermediate_size": 8192,  # Usually 4x hidden_size
            "max_position_embeddings": 2048,
            "rms_norm_eps": 1e-6,
            "rope_theta": 10000.0,
        }
    }

    config = gemma3_specs["config"]

    # Calculate parameter breakdown
    vocab_size = config["vocab_size"]
    hidden_size = config["hidden_size"]
    num_layers = config["num_layers"]
    num_heads = config["num_attention_heads"]
    intermediate_size = config["intermediate_size"]

    # Parameter calculations
    embedding_params = vocab_size * hidden_size

    # Per layer calculations
    attention_params_per_layer = 4 * hidden_size * hidden_size  # Q, K, V, O
    ffn_params_per_layer = 2 * hidden_size * intermediate_size  # gate, up, down
    rms_norm_params_per_layer = 2 * hidden_size  # attention and ffn norms

    total_layer_params = num_layers * \
        (attention_params_per_layer + ffn_params_per_layer + rms_norm_params_per_layer)
    final_norm_params = hidden_size
    lm_head_params = vocab_size * hidden_size  # Often shared with embeddings

    total_params = embedding_params + total_layer_params + \
        final_norm_params + lm_head_params

    # With weight sharing (embedding and lm_head), subtract one copy
    total_params_shared = total_params - lm_head_params

    gemma3_specs["parameter_breakdown"] = {
        "embedding_parameters": embedding_params,
        "attention_params_per_layer": attention_params_per_layer,
        "ffn_params_per_layer": ffn_params_per_layer,
        "rms_norm_params_per_layer": rms_norm_params_per_layer,
        "total_layer_params": total_layer_params,
        "final_norm_params": final_norm_params,
        "lm_head_params": lm_head_params,
        "total_without_sharing": total_params,
        "total_with_sharing": total_params_shared,
        "total_millions": round(total_params_shared / 1e6, 2)
    }

    # Print detailed breakdown
    print(f"Model: {gemma3_specs['model_name']}")
    print(f"Architecture: Transformer decoder with RMSNorm and RoPE")
    print(f"Vocabulary size: {vocab_size:,}")
    print(f"Hidden size: {hidden_size}")
    print(f"Number of layers: {num_layers}")
    print(f"Attention heads: {num_heads}")
    print(f"Intermediate size: {intermediate_size}")
    print(f"Max sequence length: {config['max_position_embeddings']}")

    print(f"\nPARAMETER BREAKDOWN:")
    print(
        f"  Embedding parameters: {embedding_params:,} ({round(embedding_params/1e6, 2)}M)")
    print(
        f"  Per-layer attention: {attention_params_per_layer:,} ({round(attention_params_per_layer/1e6, 2)}M)")
    print(
        f"  Per-layer FFN: {ffn_params_per_layer:,} ({round(ffn_params_per_layer/1e6, 2)}M)")
    print(f"  Per-layer norms: {rms_norm_params_per_layer:,}")
    print(
        f"  Total layer params: {total_layer_params:,} ({round(total_layer_params/1e6, 2)}M)")
    print(f"  Final norm: {final_norm_params:,}")
    print(f"  LM head: {lm_head_params:,} ({round(lm_head_params/1e6, 2)}M)")
    print(
        f"  TOTAL (with weight sharing): {total_params_shared:,} ({round(total_params_shared/1e6, 2)}M)")

    return gemma3_specs


def main():
    """Main function to run the complete analysis."""
    print("1B Model Architecture Analysis")
    print("=" * 60)

    # Get system info
    system_info = get_system_info()
    print("System Information:")
    for key, value in system_info.items():
        print(f"  {key}: {value}")

    # Analyze model configurations (lightweight approach)
    config_analysis = analyze_model_config()

    # Create theoretical Gemma3 analysis
    gemma3_analysis = create_gemma3_theoretical_analysis()

    # Create theoretical inference speed analysis
    print("\n" + "="*60)
    print("THEORETICAL INFERENCE SPEED ANALYSIS")
    print("="*60)

    # Theoretical calculations for 1B model on CPU
    sequence_length = 2048
    hidden_size = 2048
    num_layers = 18

    # Rough estimates for CPU inference (very approximate)
    flops_per_token = 2 * \
        gemma3_analysis["parameter_breakdown"]["total_with_sharing"]  # Forward pass
    # ~100 GFLOPS for modern CPU (very rough estimate)
    cpu_flops_per_second = 100e9

    theoretical_time_per_token = flops_per_token / cpu_flops_per_second
    theoretical_tokens_per_second = 1 / theoretical_time_per_token

    print(f"Sequence length: {sequence_length}")
    print(
        f"Model parameters: {gemma3_analysis['parameter_breakdown']['total_millions']}M")
    print(f"Estimated FLOPs per token: {flops_per_token:,.0f}")
    print(f"Estimated CPU performance: {cpu_flops_per_second/1e9:.0f} GFLOPS")
    print(
        f"Theoretical time per token: {theoretical_time_per_token*1000:.2f} ms")
    print(
        f"Theoretical tokens/second: {theoretical_tokens_per_second:.2f} tok/sec")
    print(f"Note: These are rough theoretical estimates. Actual performance depends on:")
    print(f"  - CPU architecture and optimization")
    print(f"  - Memory bandwidth")
    print(f"  - Quantization (int8 would be ~2-4x faster)")
    print(f"  - Implementation efficiency")

    # Save comprehensive results
    results = {
        "system_info": system_info,
        "config_analysis": config_analysis,
        "gemma3_theoretical": gemma3_analysis,
        "theoretical_inference": {
            "sequence_length": sequence_length,
            "flops_per_token": flops_per_token,
            "theoretical_tokens_per_second": theoretical_tokens_per_second,
            "notes": "Theoretical estimates based on rough calculations"
        },
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
    }

    with open("model_analysis_results.json", "w") as f:
        json.dump(results, f, indent=2, default=str)

    print(f"\nResults saved to model_analysis_results.json")
    print("\nAnalysis complete!")


if __name__ == "__main__":
    main()
